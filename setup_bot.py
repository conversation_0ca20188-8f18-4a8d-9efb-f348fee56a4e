#!/usr/bin/env python3
"""
Setup script for CriptoTipic4 bot
"""

import os
import sys
from dotenv import load_dotenv

def check_dependencies():
    """Check if all required dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        'flask',
        'python-dotenv',
        'requests',
        'python-telegram-bot',
        'pandas',
        'ta',
        'newsapi-python',
        'pycoingecko',
        'tweepy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} - Missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    print("✅ All dependencies are installed!")
    return True

def setup_environment():
    """Setup environment variables"""
    print("\n🔧 Setting up environment...")
    
    load_dotenv()
    
    # Check current configuration
    current_config = {
        'TELEGRAM_BOT_TOKEN': os.getenv('TELEGRAM_BOT_TOKEN'),
        'TELEGRAM_CHAT_ID': os.getenv('TELEGRAM_CHAT_ID'),
        'OPENROUTER_API_KEY': os.getenv('OPENROUTER_API_KEY'),
        'NEWS_API_KEY': os.getenv('NEWS_API_KEY'),
        'TWITTER_BEARER_TOKEN': os.getenv('TWITTER_BEARER_TOKEN')
    }
    
    print("\nCurrent configuration:")
    for key, value in current_config.items():
        if value and not value.startswith('your_'):
            print(f"  ✅ {key}: Configured")
        else:
            print(f"  ❌ {key}: Not configured")
    
    print("\n📋 Configuration Guide:")
    print("1. Telegram Bot Token:")
    print("   - Go to @BotFather on Telegram")
    print("   - Create a new bot or use existing one")
    print("   - Copy the token to TELEGRAM_BOT_TOKEN in .env")
    
    print("\n2. Telegram Chat ID:")
    print("   - Send a message to your bot")
    print("   - Visit: https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates")
    print("   - Find the chat ID and add to TELEGRAM_CHAT_ID in .env")
    
    print("\n3. OpenRouter API Key (for AI analysis):")
    print("   - Go to https://openrouter.ai/")
    print("   - Create an account and get API key")
    print("   - Add to OPENROUTER_API_KEY in .env")
    
    print("\n4. News API Key (optional):")
    print("   - Go to https://newsapi.org/")
    print("   - Get free API key")
    print("   - Add to NEWS_API_KEY in .env")
    
    print("\n5. Twitter API (optional):")
    print("   - Go to https://developer.twitter.com/")
    print("   - Create app and get Bearer Token")
    print("   - Add to TWITTER_BEARER_TOKEN in .env")

def create_systemd_service():
    """Create a systemd service file for the bot"""
    print("\n🔧 Creating systemd service...")
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    python_path = sys.executable
    
    service_content = f"""[Unit]
Description=CriptoTipic4 Trading Bot
After=network.target

[Service]
Type=simple
User={os.getenv('USER', 'ubuntu')}
WorkingDirectory={current_dir}
Environment=PATH={os.path.dirname(python_path)}
ExecStart={python_path} {current_dir}/app.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
    
    service_file = "criptotipic4.service"
    
    try:
        with open(service_file, 'w') as f:
            f.write(service_content)
        
        print(f"✅ Service file created: {service_file}")
        print("\nTo install the service:")
        print(f"1. sudo cp {service_file} /etc/systemd/system/")
        print("2. sudo systemctl daemon-reload")
        print("3. sudo systemctl enable criptotipic4")
        print("4. sudo systemctl start criptotipic4")
        print("5. sudo systemctl status criptotipic4")
        
    except Exception as e:
        print(f"❌ Error creating service file: {e}")

def main():
    """Main setup function"""
    print("🚀 CriptoTipic4 Bot Setup")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Please install missing dependencies first!")
        return
    
    # Setup environment
    setup_environment()
    
    # Create systemd service
    create_systemd_service()
    
    print("\n" + "=" * 50)
    print("✅ Setup completed!")
    print("\n🔥 Next steps:")
    print("1. Configure API keys in .env file")
    print("2. Test the bot: python test_bot.py")
    print("3. Run the bot: python app.py")
    print("4. (Optional) Install as system service")

if __name__ == "__main__":
    main()
