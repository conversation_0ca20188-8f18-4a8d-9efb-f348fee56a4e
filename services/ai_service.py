import requests
import os
from dotenv import load_dotenv

load_dotenv()

class AIService:
    def __init__(self):
        self.api_key = os.getenv('GOOGLE_API_KEY')
        self.base_url = "https://api.deepseek.com/v1/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

    def analyze_news(self, news_articles):
        """Analyze news articles using DeepSeek AI"""
        if not news_articles:
            return "No recent news available."

        # Prepare news content for analysis
        news_content = "\n".join([
            f"Title: {article['title']}\nDescription: {article.get('description', 'No description')}"
            for article in news_articles[:5]
        ])

        prompt = f"""Analyze these cryptocurrency news articles and provide a concise summary of the key points and potential market impact:

{news_content}

Please provide:
1. Main themes and trends
2. Potential market impact
3. Key events to watch
4. Overall sentiment (bullish/bearish/neutral)"""

        try:
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json={
                    "model": "deepseek-chat-v3-0324",
                    "messages": [{"role": "user", "content": prompt}],
                    "temperature": 0.7,
                    "max_tokens": 500
                }
            )
            response.raise_for_status()
            return response.json()['choices'][0]['message']['content']
        except Exception as e:
            print(f"Error analyzing news: {e}")
            return "Unable to analyze news at this time."

    def analyze_market_sentiment(self, technical_analysis):
        """Analyze market sentiment using DeepSeek AI"""
        prompt = f"""Based on this technical analysis, provide a clear and beginner-friendly market sentiment analysis:

{technical_analysis}

Please provide:
1. Overall market sentiment
2. Key technical indicators and their meaning
3. Risk level assessment
4. Trading recommendation
5. Important levels to watch

Use simple language and explain technical terms."""

        try:
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json={
                    "model": "deepseek-chat-v3-0324",
                    "messages": [{"role": "user", "content": prompt}],
                    "temperature": 0.7,
                    "max_tokens": 500
                }
            )
            response.raise_for_status()
            return response.json()['choices'][0]['message']['content']
        except Exception as e:
            print(f"Error analyzing sentiment: {e}")
            return "Unable to analyze market sentiment at this time."

    def analyze_twitter_sentiment(self, tweets: list) -> str:
        """
        Analyze Twitter sentiment for a symbol using DeepSeek AI and return a summary.
        """
        if not tweets:
            return "No recent tweets found for sentiment analysis."

        tweets_text = "\n".join(tweets)
        prompt = f"""
        Analyze the overall sentiment from the following tweets related to a cryptocurrency. Identify key themes and whether the sentiment is generally positive, negative, or neutral, and explain why:
        {tweets_text}
        """
        try:
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json={
                    "model": "deepseek-chat-v3-0324",
                    "messages": [{"role": "user", "content": prompt}],
                    "temperature": 0.7,
                    "max_tokens": 500
                }
            )
            response.raise_for_status()
            return response.json()['choices'][0]['message']['content']
        except Exception as e:
            print(f"Error analyzing Twitter sentiment: {e}")
            return "Unable to analyze Twitter sentiment at this time."

    def generate_trading_insights(self, symbol, market_data, news_analysis, sentiment_analysis, twitter_sentiment):
        """
        Generate comprehensive trading insights using DeepSeek AI
        """
        prompt = f"""
        Based on the following information about {symbol}, provide comprehensive trading insights:

        Market Data:
        {market_data}

        News Analysis:
        {news_analysis}

        Sentiment Analysis:
        {sentiment_analysis}

        Twitter Sentiment:
        {twitter_sentiment}

        Please provide:
        1. Trading opportunity assessment
        2. Entry and exit points
        3. Risk management recommendations
        4. Key factors to watch
        5. Beginner-friendly explanation of the analysis

        Format the response in a clear, structured way with emojis for better readability."""

        try:
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json={
                    "model": "deepseek-chat-v3-0324",
                    "messages": [{"role": "user", "content": prompt}],
                    "temperature": 0.7,
                    "max_tokens": 800
                }
            )
            response.raise_for_status()
            return response.json()['choices'][0]['message']['content']
        except Exception as e:
            print(f"Error generating insights: {e}")
            return "Unable to generate trading insights at this time." 