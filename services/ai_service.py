import requests
import os
from dotenv import load_dotenv

load_dotenv()

class AIService:
    def __init__(self):
        self.api_key = os.getenv('OPENROUTER_API_KEY')
        self.base_url = "https://openrouter.ai/api/v1/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/your-repo",  # Optional
            "X-Title": "CriptoTipic4 Bot"  # Optional
        }

    def analyze_news(self, news_articles):
        """Analyze news articles using AI"""
        if not news_articles:
            return "No recent news available."

        # Check if API key is configured
        if not self.api_key or self.api_key == 'your_openrouter_api_key_here':
            return "AI analysis not available - API key not configured."

        # Prepare news content for analysis
        news_content = "\n".join([
            f"Title: {article['title']}\nDescription: {article.get('description', 'No description')}"
            for article in news_articles[:5]
        ])

        prompt = f"""Analyze these cryptocurrency news articles and provide a concise summary of the key points and potential market impact:

{news_content}

Please provide:
1. Main themes and trends
2. Potential market impact
3. Key events to watch
4. Overall sentiment (bullish/bearish/neutral)"""

        try:
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json={
                    "model": "anthropic/claude-3.5-sonnet",
                    "messages": [{"role": "user", "content": prompt}],
                    "temperature": 0.7,
                    "max_tokens": 500
                },
                timeout=30
            )
            response.raise_for_status()
            return response.json()['choices'][0]['message']['content']
        except requests.exceptions.Timeout:
            return "News analysis timed out - please try again later."
        except requests.exceptions.RequestException as e:
            if "402" in str(e) or "Payment Required" in str(e):
                return "📰 News analysis requires API credits. Please add credits to your OpenRouter account or configure a different AI service."
            print(f"Error analyzing news: {e}")
            return "Unable to analyze news at this time."
        except Exception as e:
            print(f"Unexpected error analyzing news: {e}")
            return "Unable to analyze news at this time."

    def analyze_market_sentiment(self, technical_analysis):
        """Analyze market sentiment using AI"""
        # Check if API key is configured
        if not self.api_key or self.api_key == 'your_openrouter_api_key_here':
            return "AI analysis not available - API key not configured."

        prompt = f"""Based on this technical analysis, provide a clear and beginner-friendly market sentiment analysis:

{technical_analysis}

Please provide:
1. Overall market sentiment
2. Key technical indicators and their meaning
3. Risk level assessment
4. Trading recommendation
5. Important levels to watch

Use simple language and explain technical terms."""

        try:
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json={
                    "model": "anthropic/claude-3.5-sonnet",
                    "messages": [{"role": "user", "content": prompt}],
                    "temperature": 0.7,
                    "max_tokens": 500
                },
                timeout=30
            )
            response.raise_for_status()
            return response.json()['choices'][0]['message']['content']
        except requests.exceptions.Timeout:
            return "Market sentiment analysis timed out - please try again later."
        except requests.exceptions.RequestException as e:
            if "402" in str(e) or "Payment Required" in str(e):
                return "📈 Market sentiment analysis requires API credits. Please add credits to your OpenRouter account."
            print(f"Error analyzing sentiment: {e}")
            return "Unable to analyze market sentiment at this time."
        except Exception as e:
            print(f"Unexpected error analyzing sentiment: {e}")
            return "Unable to analyze market sentiment at this time."

    def analyze_twitter_sentiment(self, tweets: list) -> str:
        """
        Analyze Twitter sentiment for a symbol using AI and return a summary.
        """
        if not tweets:
            return "No recent tweets found for sentiment analysis."

        # Check if API key is configured
        if not self.api_key or self.api_key == 'your_openrouter_api_key_here':
            return "AI analysis not available - API key not configured."

        tweets_text = "\n".join(tweets)
        prompt = f"""
        Analyze the overall sentiment from the following tweets related to a cryptocurrency. Identify key themes and whether the sentiment is generally positive, negative, or neutral, and explain why:
        {tweets_text}
        """
        try:
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json={
                    "model": "anthropic/claude-3.5-sonnet",
                    "messages": [{"role": "user", "content": prompt}],
                    "temperature": 0.7,
                    "max_tokens": 500
                },
                timeout=30
            )
            response.raise_for_status()
            return response.json()['choices'][0]['message']['content']
        except requests.exceptions.Timeout:
            return "Twitter sentiment analysis timed out - please try again later."
        except requests.exceptions.RequestException as e:
            if "402" in str(e) or "Payment Required" in str(e):
                return "🐦 Twitter sentiment analysis requires API credits. Please add credits to your OpenRouter account."
            print(f"Error analyzing Twitter sentiment: {e}")
            return "Unable to analyze Twitter sentiment at this time."
        except Exception as e:
            print(f"Unexpected error analyzing Twitter sentiment: {e}")
            return "Unable to analyze Twitter sentiment at this time."

    def generate_trading_insights(self, symbol, market_data, news_analysis, sentiment_analysis, twitter_sentiment):
        """
        Generate comprehensive trading insights using AI
        """
        # Check if API key is configured
        if not self.api_key or self.api_key == 'your_openrouter_api_key_here':
            return "AI analysis not available - API key not configured."

        prompt = f"""
        Based on the following information about {symbol}, provide comprehensive trading insights:

        Market Data:
        {market_data}

        News Analysis:
        {news_analysis}

        Sentiment Analysis:
        {sentiment_analysis}

        Twitter Sentiment:
        {twitter_sentiment}

        Please provide:
        1. Trading opportunity assessment
        2. Entry and exit points
        3. Risk management recommendations
        4. Key factors to watch
        5. Beginner-friendly explanation of the analysis

        Format the response in a clear, structured way with emojis for better readability."""

        try:
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json={
                    "model": "anthropic/claude-3.5-sonnet",
                    "messages": [{"role": "user", "content": prompt}],
                    "temperature": 0.7,
                    "max_tokens": 800
                },
                timeout=30
            )
            response.raise_for_status()
            return response.json()['choices'][0]['message']['content']
        except requests.exceptions.Timeout:
            return "Trading insights generation timed out - please try again later."
        except requests.exceptions.RequestException as e:
            if "402" in str(e) or "Payment Required" in str(e):
                return """🤖 AI Trading Insights (Limited Mode)

⚠️ Full AI analysis requires API credits. Please add credits to your OpenRouter account for detailed insights.

📊 Basic Technical Analysis Available:
• Check RSI levels (below 30 = oversold, above 70 = overbought)
• Monitor EMA crossovers (price above EMA200 = bullish trend)
• Watch support and resistance levels
• Consider volume patterns

💡 Manual Analysis Tips:
• Always use stop-loss orders
• Don't invest more than you can afford to lose
• Do your own research (DYOR)
• Consider market sentiment and news"""
            print(f"Error generating insights: {e}")
            return "Unable to generate trading insights at this time."
        except Exception as e:
            print(f"Unexpected error generating insights: {e}")
            return "Unable to generate trading insights at this time."