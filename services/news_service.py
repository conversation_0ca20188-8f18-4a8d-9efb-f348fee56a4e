import requests
import feedparser
from bs4 import BeautifulSoup
from datetime import datetime, timedelta
import re
from textblob import TextBlob
import time
from pycoingecko import CoinGeckoAPI
import tweepy
import os
from dotenv import load_dotenv

load_dotenv()

class CryptoNewsService:
    def __init__(self):
        self.cg = CoinGeckoAPI()
        
        # Twitter setup (opcional)
        twitter_bearer_token = os.getenv('TWITTER_BEARER_TOKEN')
        self.twitter_client = tweepy.Client(twitter_bearer_token) if twitter_bearer_token and twitter_bearer_token != 'your_twitter_bearer_token_here' else None
        
        # Headers para web scraping
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

    def get_coingecko_news(self, symbol, limit=5):
        """Busca notícias do CoinGecko usando a API gratuita"""
        try:
            # Primeiro, encontrar o coin_id
            coin_list = self.cg.get_coins_list()
            coin_id = next((coin['id'] for coin in coin_list if coin['symbol'].upper() == symbol.upper()), None)
            
            if not coin_id:
                return []
            
            # Buscar informações da moeda que podem incluir links e descrições
            coin_data = self.cg.get_coin_by_id(coin_id)
            
            news_items = []
            
            # Extrair informações relevantes
            if coin_data:
                description = coin_data.get('description', {}).get('en', '')
                if description:
                    # Extrair links de notícias da descrição se houver
                    links = re.findall(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', description)
                    
                    news_items.append({
                        'title': f'{coin_data.get("name", symbol)} - Informações Oficiais',
                        'description': description[:200] + '...' if len(description) > 200 else description,
                        'url': coin_data.get('links', {}).get('homepage', [''])[0] if coin_data.get('links', {}).get('homepage') else '',
                        'published_at': datetime.now().isoformat(),
                        'source': 'CoinGecko'
                    })
            
            return news_items[:limit]
            
        except Exception as e:
            print(f"Erro ao buscar notícias do CoinGecko: {e}")
            return []

    def get_cointelegraph_news(self, symbol, limit=5):
        """Busca notícias do CoinTelegraph usando RSS feed com priorização por impacto"""
        try:
            # CoinTelegraph RSS feed
            rss_url = "https://cointelegraph.com/rss"

            # Parse do RSS feed
            feed = feedparser.parse(rss_url)

            news_items = []
            symbol_lower = symbol.lower()

            # Palavras-chave de alto impacto
            high_impact_keywords = [
                'breaking', 'urgent', 'alert', 'crash', 'surge', 'pump', 'dump',
                'regulation', 'ban', 'approval', 'etf', 'sec', 'government',
                'hack', 'exploit', 'security', 'breach', 'stolen',
                'partnership', 'adoption', 'integration', 'launch',
                'all-time high', 'ath', 'record', 'milestone'
            ]

            # Processar todas as entradas e calcular score de impacto
            scored_news = []

            for entry in feed.entries[:30]:  # Verificar mais entradas
                title = entry.title.lower()
                description = entry.get('description', '').lower()

                # Calcular score de relevância e impacto
                relevance_score = 0
                impact_score = 0

                # Score de relevância para o símbolo
                if symbol_lower in title:
                    relevance_score += 10
                elif symbol_lower in description:
                    relevance_score += 5
                elif self._is_crypto_related(entry.title, symbol):
                    relevance_score += 3

                # Score de impacto baseado em palavras-chave
                for keyword in high_impact_keywords:
                    if keyword in title:
                        impact_score += 5
                    elif keyword in description:
                        impact_score += 2

                # Score adicional para notícias recentes
                try:
                    pub_date = datetime.strptime(entry.get('published', ''), '%a, %d %b %Y %H:%M:%S %z')
                    hours_ago = (datetime.now(pub_date.tzinfo) - pub_date).total_seconds() / 3600
                    if hours_ago < 24:
                        impact_score += 3
                    elif hours_ago < 72:
                        impact_score += 1
                except:
                    pass

                total_score = relevance_score + impact_score

                if total_score > 0:  # Só incluir se tiver alguma relevância
                    # Criar resumo curto da descrição
                    description_text = entry.get('description', '')
                    if description_text:
                        # Remover HTML tags
                        from bs4 import BeautifulSoup
                        clean_desc = BeautifulSoup(description_text, 'html.parser').get_text()
                        # Limitar a 100 caracteres
                        short_summary = clean_desc[:100] + '...' if len(clean_desc) > 100 else clean_desc
                    else:
                        short_summary = "Sem descrição disponível"

                    scored_news.append({
                        'title': entry.title,
                        'description': short_summary,
                        'url': entry.link,
                        'published_at': entry.get('published', ''),
                        'source': 'CoinTelegraph',
                        'impact_score': total_score,
                        'relevance_score': relevance_score
                    })

            # Ordenar por score de impacto (maior primeiro)
            scored_news.sort(key=lambda x: x['impact_score'], reverse=True)

            # Se não encontrou notícias específicas, pegar as de maior impacto geral
            if not scored_news:
                for entry in feed.entries[:limit]:
                    description_text = entry.get('description', '')
                    if description_text:
                        from bs4 import BeautifulSoup
                        clean_desc = BeautifulSoup(description_text, 'html.parser').get_text()
                        short_summary = clean_desc[:100] + '...' if len(clean_desc) > 100 else clean_desc
                    else:
                        short_summary = "Notícia geral sobre criptomoedas"

                    scored_news.append({
                        'title': entry.title,
                        'description': short_summary,
                        'url': entry.link,
                        'published_at': entry.get('published', ''),
                        'source': 'CoinTelegraph',
                        'impact_score': 1
                    })

            return scored_news[:limit]

        except Exception as e:
            print(f"Erro ao buscar notícias do CoinTelegraph: {e}")
            return []

    def get_twitter_news(self, symbol, limit=10):
        """Busca tweets relacionados ao símbolo"""
        if not self.twitter_client:
            return []
            
        try:
            # Buscar tweets com hashtags e menções relevantes
            queries = [
                f"#{symbol} crypto",
                f"${symbol}",
                f"{symbol} cryptocurrency",
                f"{symbol} price"
            ]
            
            tweets = []
            
            for query in queries:
                try:
                    response = self.twitter_client.search_recent_tweets(
                        query=f"{query} lang:en -is:retweet",
                        tweet_fields=["created_at", "text", "public_metrics"],
                        max_results=min(limit, 10)
                    )
                    
                    if response.data:
                        for tweet in response.data:
                            tweets.append({
                                'title': f'Tweet sobre {symbol}',
                                'description': tweet.text,
                                'url': f'https://twitter.com/i/web/status/{tweet.id}',
                                'published_at': tweet.created_at.isoformat() if tweet.created_at else datetime.now().isoformat(),
                                'source': 'Twitter'
                            })
                    
                    time.sleep(1)  # Rate limiting
                    
                except Exception as e:
                    print(f"Erro na busca do Twitter para query '{query}': {e}")
                    continue
            
            return tweets[:limit]
            
        except Exception as e:
            print(f"Erro ao buscar tweets: {e}")
            return []

    def _is_crypto_related(self, title, symbol):
        """Verifica se o título está relacionado ao símbolo ou crypto em geral"""
        crypto_keywords = ['bitcoin', 'ethereum', 'crypto', 'blockchain', 'defi', 'nft', 'solana', 'binance']
        title_lower = title.lower()
        
        # Verificar se contém o símbolo ou palavras-chave crypto
        return any(keyword in title_lower for keyword in crypto_keywords)

    def analyze_sentiment(self, news_items):
        """Análise de sentimento simples usando TextBlob (gratuito)"""
        if not news_items:
            return {
                'overall_sentiment': 'neutral',
                'positive_count': 0,
                'negative_count': 0,
                'neutral_count': 0,
                'sentiment_score': 0.0
            }
        
        sentiments = []
        positive_count = 0
        negative_count = 0
        neutral_count = 0
        
        for item in news_items:
            text = f"{item.get('title', '')} {item.get('description', '')}"
            
            try:
                blob = TextBlob(text)
                polarity = blob.sentiment.polarity
                
                sentiments.append(polarity)
                
                if polarity > 0.1:
                    positive_count += 1
                elif polarity < -0.1:
                    negative_count += 1
                else:
                    neutral_count += 1
                    
            except Exception as e:
                print(f"Erro na análise de sentimento: {e}")
                neutral_count += 1
        
        avg_sentiment = sum(sentiments) / len(sentiments) if sentiments else 0.0
        
        # Determinar sentimento geral
        if avg_sentiment > 0.1:
            overall_sentiment = 'positive'
        elif avg_sentiment < -0.1:
            overall_sentiment = 'negative'
        else:
            overall_sentiment = 'neutral'
        
        return {
            'overall_sentiment': overall_sentiment,
            'positive_count': positive_count,
            'negative_count': negative_count,
            'neutral_count': neutral_count,
            'sentiment_score': avg_sentiment
        }

    def get_comprehensive_news(self, symbol, limit_per_source=3):
        """Busca notícias de todas as fontes e combina os resultados"""
        all_news = []
        
        print(f"🔍 Buscando notícias para {symbol}...")
        
        # CoinGecko
        try:
            coingecko_news = self.get_coingecko_news(symbol, limit_per_source)
            all_news.extend(coingecko_news)
            print(f"  ✅ CoinGecko: {len(coingecko_news)} notícias")
        except Exception as e:
            print(f"  ❌ CoinGecko: {e}")
        
        # CoinTelegraph
        try:
            cointelegraph_news = self.get_cointelegraph_news(symbol, limit_per_source)
            all_news.extend(cointelegraph_news)
            print(f"  ✅ CoinTelegraph: {len(cointelegraph_news)} notícias")
        except Exception as e:
            print(f"  ❌ CoinTelegraph: {e}")
        
        # Twitter
        try:
            twitter_news = self.get_twitter_news(symbol, limit_per_source)
            all_news.extend(twitter_news)
            print(f"  ✅ Twitter: {len(twitter_news)} tweets")
        except Exception as e:
            print(f"  ❌ Twitter: {e}")
        
        # Análise de sentimento
        sentiment_analysis = self.analyze_sentiment(all_news)
        
        return {
            'news': all_news,
            'sentiment': sentiment_analysis,
            'total_items': len(all_news)
        }

    def format_news_summary(self, news_data, symbol):
        """Formata um resumo detalhado das notícias para exibição"""
        if not news_data['news']:
            return f"📰 Nenhuma notícia recente encontrada para {symbol}"

        sentiment = news_data['sentiment']
        sentiment_emoji = {
            'positive': '📈',
            'negative': '📉',
            'neutral': '➡️'
        }

        # Ordenar notícias por impacto se disponível
        sorted_news = sorted(news_data['news'],
                           key=lambda x: x.get('impact_score', 0),
                           reverse=True)

        summary = f"""📰 Notícias de Alto Impacto - {symbol}

{sentiment_emoji.get(sentiment['overall_sentiment'], '➡️')} Sentimento: {sentiment['overall_sentiment'].title()} | 📊 Total: {news_data['total_items']} notícias
✅ {sentiment['positive_count']} positivas | ❌ {sentiment['negative_count']} negativas | ➡️ {sentiment['neutral_count']} neutras

🔥 TOP 5 NOTÍCIAS DE MAIOR IMPACTO:"""

        # Mostrar até 5 notícias com resumos curtos
        for i, news_item in enumerate(sorted_news[:5]):
            # Calcular impacto em escala de 1-10
            raw_impact = news_item.get('impact_score', 0)
            impact_10_scale = min(10, max(1, int((raw_impact / 20) * 10))) if raw_impact > 0 else 1

            impact_emoji = "🚨" if impact_10_scale >= 8 else "⚡" if impact_10_scale >= 5 else "📌"

            # Título limitado
            title = news_item['title'][:70] + '...' if len(news_item['title']) > 70 else news_item['title']

            # Resumo da descrição
            description = news_item.get('description', 'Sem descrição')
            if len(description) > 80:
                description = description[:80] + '...'

            summary += f"""

{i+1}. {impact_emoji} {title}
   💬 {description}
   🔗 {news_item['source']} | 🎯 Impacto: ({impact_10_scale}/10)"""

        # Adicionar timestamp
        summary += f"\n\n🕒 Atualizado: {datetime.now().strftime('%H:%M:%S')}"

        return summary
