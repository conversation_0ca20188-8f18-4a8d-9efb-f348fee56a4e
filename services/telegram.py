import os
import requests
from dotenv import load_dotenv
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, ContextTypes, MessageHandler, filters
from services.settings import get_user_settings, update_user_settings
from services.ai import analyze_token, get_ai_strategy, analyze_pool_risk
from .trading import TradingAnalyzer
import threading

load_dotenv()

TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
TELEGRAM_CHAT_ID = os.getenv('TELEGRAM_CHAT_ID')

class TelegramBot:
    def __init__(self):
        self.token = TELEGRAM_BOT_TOKEN
        self.trading_analyzer = TradingAnalyzer()
        
    async def start(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Send a message when the command /start is issued."""

        welcome_message = """
🌟 *Welcome to the Solana Trading Assistant Bot!* 🌟

I'm your AI-powered trading companion, ready to help you navigate the Solana ecosystem with advanced analytics and real-time insights.

📊 *Available Commands:*
/analyze <symbol> - Get detailed analysis for any token
/help - Show detailed help information
/start - Show this welcome message
/config - Configure your trading preferences
/alerts - Set up price and liquidity alerts

🔍 *Quick Analysis:*
Just send any token symbol (e.g., SOL, BONK, SAMO) to get instant analysis!

📈 *Features:*
• Real-time market analysis and price tracking
• Technical indicators (RSI, MACD, EMAs, Volume)
• Latest news and social sentiment
• Trading opportunities with entry/exit points
• Risk assessment and security checks
• Pool analysis and liquidity monitoring
• Price alerts and notifications
• Historical data analysis

💡 *Examples:*
• Send "SOL" for Solana analysis
• Use "/analyze BONK" for detailed token analysis
• Set up alerts with "/alerts"

⚠️ *Important Notes:*
• Always do your own research (DYOR)
• Past performance doesn't guarantee future results
• Use stop-loss and take-profit strategies

Need help? Use /help for detailed information about all features!

Happy trading! 🚀
"""
        await update.message.reply_text(welcome_message, parse_mode='Markdown')

    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Send a message when the command /help is issued."""

        help_text = """
🤖 Trading Assistant Bot Help

Commands:
/analyze <symbol> - Get detailed analysis for a symbol
/help - Show this help message

Just send any cryptocurrency symbol (e.g., BTC, ETH, SOL) to get instant analysis!

The analysis includes:
• Market trend and context
• Technical indicators (RSI, MACD, EMAs)
• Latest news
• Trading opportunities with entry/exit points
• Risk assessment
"""
        await update.message.reply_text(help_text)

    async def analyze_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle the /analyze command."""

        if not context.args:
            await update.message.reply_text("Please provide a symbol. Example: /analyze BTC")
            return

        symbol = context.args[0].upper()
        await self.send_analysis(update, symbol)

    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle regular messages."""

        text = update.message.text.strip().upper()
        if text in ['BTC', 'ETH', 'SOL', 'BNB', 'XRP', 'ADA', 'DOT', 'DOGE']:
            await self.send_analysis(update, text)
        else:
            await update.message.reply_text(
                "Please send a valid cryptocurrency symbol (e.g., BTC, ETH, SOL) or use /analyze <symbol>"
            )

    async def send_analysis(self, update: Update, symbol: str):
        """Send market analysis for a symbol."""
        await update.message.reply_text(f"🔍 Analyzing {symbol}... Please wait...")
        
        analysis = self.trading_analyzer.analyze_market(symbol)
        if analysis:
            message = self.trading_analyzer.format_analysis_message(analysis)
            await update.message.reply_text(message)
        else:
            await update.message.reply_text(f"❌ Could not fetch data for {symbol}. Please try again later.")

    def start_bot(self):
        """Start the bot."""
        application = Application.builder().token(self.token).build()

        # Add handlers
        application.add_handler(CommandHandler("start", self.start))
        application.add_handler(CommandHandler("help", self.help_command))
        application.add_handler(CommandHandler("analyze", self.analyze_command))
        application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message))

        # Start the bot
        application.run_polling()

    def run(self):
        self.start_bot()

def setup_handlers():
    """Set up command handlers."""
    bot.add_handler(CommandHandler("start", start))
    bot.add_handler(CommandHandler("config", config_command))
    bot.add_handler(CallbackQueryHandler(button_callback))

def send_telegram_alert(pool: dict):
    """
    Send an alert to the Telegram group with pool info.
    """
    if not TELEGRAM_BOT_TOKEN or not TELEGRAM_CHAT_ID:
        print("Telegram bot token or chat id not set.")
        return

    # Get AI analysis
    ai_analysis = analyze_pool_risk(pool)
    
    msg = (
        f"🚨 New Pool Alert! 🚨\n"
        f"Token: {pool.get('name')}\n"
        f"Pair Address: {pool.get('address')}\n"
        f"Liquidity: ${pool.get('liquidity'):,}\n"
        f"24h Volume: ${pool.get('volume'):,}\n"
        f"Holders: {pool.get('holders', 'N/A')}\n"
        f"AI Analysis: {ai_analysis}\n"
        f"[Dexscreener](https://dexscreener.com/solana/{pool.get('address')}) | "
        f"[Birdeye](https://birdeye.so/token/{pool.get('tokenA')}) | "
        f"[Jupiter](https://jup.ag/swap/SOL-{pool.get('tokenA')})\n"
        f"[Raydium]({pool.get('source_link')})"
    )
    
    url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage"
    payload = {
        "chat_id": TELEGRAM_CHAT_ID,
        "text": msg,
        "parse_mode": "Markdown",
        "disable_web_page_preview": True
    }
    try:
        requests.post(url, json=payload, timeout=10)
    except Exception as e:
        print(f"Error sending Telegram alert: {e}")

# Start the bot
def start_bot():
    setup_handlers()
    bot.run_polling() 