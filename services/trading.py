import pandas as pd
import numpy as np
from ta.trend import EMAIndicator
from ta.momentum import RSIIndicator, StochasticOscillator
from ta.trend import MACD
from datetime import datetime, timedelta
import os
from dotenv import load_dotenv
from .news_service import CryptoNewsService
from .analysis_service import TechnicalAnalysisService
from .stocks_forex_service import StocksForexService
from pycoingecko import CoinGeckoAPI

load_dotenv()

TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
TELEGRAM_CHAT_ID = os.getenv('TELEGRAM_CHAT_ID')

class TradingAnalyzer:
    def __init__(self):
        # Initialize services - agora usando apenas APIs gratuitas
        self.cg = CoinGeckoAPI()
        self.news_service = CryptoNewsService()
        self.analysis_service = TechnicalAnalysisService()
        self.stocks_forex_service = StocksForexService()
        
    def get_market_data(self, symbol):
        """Fetch comprehensive market data for the given symbol from CoinGecko"""
        try:
            # Get Coin ID from symbol - buscar mais precisamente
            coin_list = self.cg.get_coins_list()

            # Primeiro tentar match exato do símbolo
            coin_id = next((coin['id'] for coin in coin_list if coin['symbol'].upper() == symbol.upper()), None)

            # Se não encontrar, tentar buscar por nome também
            if not coin_id:
                symbol_lower = symbol.lower()
                coin_id = next((coin['id'] for coin in coin_list
                              if symbol_lower in coin['id'].lower() or
                              symbol_lower in coin['name'].lower()), None)

            if not coin_id:
                print(f"❌ Não foi possível encontrar dados para: {symbol}")
                return None

            print(f"✅ Encontrado CoinGecko ID: {coin_id} para {symbol}")

            # Obter dados de mercado atuais (OBRIGATÓRIO)
            try:
                current_data = self.cg.get_coin_by_id(coin_id, localization=False, tickers=False,
                                                    market_data=True, community_data=False,
                                                    developer_data=False, sparkline=False)

                market_data = current_data.get('market_data', {})

                # Extrair informações essenciais
                current_price = market_data.get('current_price', {}).get('usd', 0)
                market_cap = market_data.get('market_cap', {}).get('usd', 0)
                volume_24h = market_data.get('total_volume', {}).get('usd', 0)
                price_change_24h = market_data.get('price_change_percentage_24h', 0)
                price_change_7d = market_data.get('price_change_percentage_7d', 0)
                price_change_30d = market_data.get('price_change_percentage_30d', 0)

                print(f"💰 Preço atual: ${current_price:.6f}")
                print(f"📊 Market Cap: ${market_cap:,.0f}")
                print(f"📈 Volume 24h: ${volume_24h:,.0f}")
                print(f"📉 Mudança 24h: {price_change_24h:.2f}%")

            except Exception as e:
                print(f"⚠️ Erro ao obter dados atuais: {e}")
                current_price = 0
                market_cap = 0
                volume_24h = 0
                price_change_24h = 0
                price_change_7d = 0
                price_change_30d = 0

            # Fetch historical data para análise técnica
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)

            prices = self.cg.get_coin_market_chart_range_by_id(
                id=coin_id,
                vs_currency='usd',
                from_timestamp=start_date.timestamp(),
                to_timestamp=end_date.timestamp()
            )

            if not prices or 'prices' not in prices or not prices['prices']:
                print("⚠️ Dados históricos não disponíveis, usando apenas dados atuais")
                # Criar DataFrame mínimo com dados atuais
                df = pd.DataFrame({
                    'Close': [current_price],
                    'Open': [current_price],
                    'High': [current_price],
                    'Low': [current_price],
                    'Volume': [volume_24h]
                }, index=[datetime.now()])
            else:
                # Processar dados históricos
                df = pd.DataFrame(prices['prices'], columns=['timestamp', 'Close'])
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                df.set_index('timestamp', inplace=True)

                # Simular OHLC a partir dos dados de preço
                df['Open'] = df['Close'].shift(1).fillna(df['Close'])
                df['High'] = df['Close'] * 1.02  # Simular alta de 2%
                df['Low'] = df['Close'] * 0.98   # Simular baixa de 2%

                # Adicionar volume
                if 'total_volumes' in prices:
                    volume_df = pd.DataFrame(prices['total_volumes'], columns=['timestamp', 'Volume'])
                    volume_df['timestamp'] = pd.to_datetime(volume_df['timestamp'], unit='ms')
                    volume_df.set_index('timestamp', inplace=True)
                    df = df.join(volume_df['Volume'], how='left')
                else:
                    df['Volume'] = volume_24h

            # Adicionar informações de mercado ao DataFrame
            df.attrs = {
                'symbol': symbol.upper(),
                'coin_id': coin_id,
                'current_price': current_price,
                'market_cap': market_cap,
                'volume_24h': volume_24h,
                'price_change_24h': price_change_24h,
                'price_change_7d': price_change_7d,
                'price_change_30d': price_change_30d,
                'name': current_data.get('name', symbol) if 'current_data' in locals() else symbol
            }

            print(f"✅ Dados processados: {len(df)} pontos históricos")
            return df

        except Exception as e:
            print(f"❌ Erro ao buscar dados de mercado: {e}")
            return None

    def calculate_indicators(self, df):
        """Calculate technical indicators"""
        # RSI
        rsi = RSIIndicator(close=df['Close'], window=14)
        df['RSI'] = rsi.rsi()

        # EMAs
        ema50 = EMAIndicator(close=df['Close'], window=50)
        ema200 = EMAIndicator(close=df['Close'], window=200)
        df['EMA50'] = ema50.ema_indicator()
        df['EMA200'] = ema200.ema_indicator()

        # MACD
        macd = MACD(close=df['Close'])
        df['MACD'] = macd.macd()
        df['MACD_Signal'] = macd.macd_signal()

        # Stochastic RSI
        stoch_rsi = StochasticOscillator(high=df['High'], low=df['Low'], close=df['Close'])
        df['Stoch_RSI'] = stoch_rsi.stoch()

        return df

    def get_comprehensive_news(self, symbol):
        """Busca notícias de todas as fontes usando o novo serviço"""
        try:
            return self.news_service.get_comprehensive_news(symbol)
        except Exception as e:
            print(f"Erro ao buscar notícias: {e}")
            return {'news': [], 'sentiment': {'overall_sentiment': 'neutral'}, 'total_items': 0}

    def detect_asset_type(self, symbol):
        """Detecta o tipo de ativo: crypto, stock ou forex"""
        symbol_upper = symbol.upper()

        # Forex pairs
        forex_pairs = ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD', 'EURJPY', 'GBPJPY', 'EURGBP']
        if symbol_upper in forex_pairs:
            return 'forex'

        # Crypto symbols comuns
        crypto_symbols = ['BTC', 'ETH', 'SOL', 'ADA', 'DOT', 'LINK', 'UNI', 'AVAX', 'MATIC', 'ATOM', 'DOGE', 'SHIB', 'LTC']
        if symbol_upper in crypto_symbols:
            return 'crypto'

        # Por padrão, assumir stock
        return 'stock'

    def analyze_market(self, symbol):
        """Perform complete market analysis for crypto, stocks, and forex"""
        asset_type = self.detect_asset_type(symbol)
        print(f"🔍 Detectado como: {asset_type}")

        if asset_type == 'crypto':
            return self.analyze_crypto_market(symbol)
        else:
            return self.analyze_stocks_forex_market(symbol, asset_type)

    def analyze_crypto_market(self, symbol):
        """Análise específica para criptomoedas"""
        # Get comprehensive market data
        df = self.get_market_data(symbol)

        if df is None:
            return None

        # Extrair informações de mercado dos atributos do DataFrame
        market_info = df.attrs

        # Calculate indicators
        df = self.calculate_indicators(df)

        # Get latest values from technical indicators
        current_price = market_info.get('current_price', df['Close'].iloc[-1])
        rsi = df['RSI'].iloc[-1] if not df['RSI'].isna().iloc[-1] else 50
        ema50 = df['EMA50'].iloc[-1] if not df['EMA50'].isna().iloc[-1] else current_price
        ema200 = df['EMA200'].iloc[-1] if not df['EMA200'].isna().iloc[-1] else current_price
        macd = df['MACD'].iloc[-1] if not df['MACD'].isna().iloc[-1] else 0
        macd_signal = df['MACD_Signal'].iloc[-1] if not df['MACD_Signal'].isna().iloc[-1] else 0
        stoch_rsi = df['Stoch_RSI'].iloc[-1] if not df['Stoch_RSI'].isna().iloc[-1] else 50

        # Determine trend
        trend = "Bullish" if current_price > ema200 else "Bearish"

        # Calculate support and resistance
        support = df['Low'].rolling(window=min(20, len(df))).min().iloc[-1]
        resistance = df['High'].rolling(window=min(20, len(df))).max().iloc[-1]

        # Get comprehensive news from multiple sources
        news_data = self.get_comprehensive_news(symbol)

        # Prepare comprehensive technical analysis data
        technical_data = {
            'current_price': current_price,
            'rsi': rsi,
            'ema50': ema50,
            'ema200': ema200,
            'macd': macd,
            'macd_signal': macd_signal,
            'support': support,
            'resistance': resistance
        }

        # Get technical analysis recommendation
        technical_recommendation = self.analysis_service.generate_trading_recommendation(technical_data)

        # Format technical analysis
        technical_analysis_text = self.analysis_service.format_technical_analysis(
            symbol, technical_data, technical_recommendation
        )

        # Format news summary
        news_summary = self.news_service.format_news_summary(news_data, symbol)

        # Prepare comprehensive analysis
        analysis = {
            'asset_type': 'crypto',
            'symbol': symbol.upper(),
            'coin_name': market_info.get('name', symbol),
            'current_price': current_price,
            'market_cap': market_info.get('market_cap', 0),
            'volume_24h': market_info.get('volume_24h', df['Volume'].iloc[-1] if len(df) > 0 else 0),
            'price_change_24h': market_info.get('price_change_24h', 0),
            'price_change_7d': market_info.get('price_change_7d', 0),
            'price_change_30d': market_info.get('price_change_30d', 0),
            'trend': trend,
            'rsi': rsi,
            'ema50': ema50,
            'ema200': ema200,
            'macd': macd,
            'macd_signal': macd_signal,
            'stoch_rsi': stoch_rsi,
            'support': support,
            'resistance': resistance,
            'volatility': df['Close'].pct_change().std() * 100 if len(df) > 1 else 0,
            'news_data': news_data,
            'news_summary': news_summary,
            'technical_analysis': technical_analysis_text,
            'technical_recommendation': technical_recommendation,
            'trading_insights': technical_analysis_text
        }

        return analysis

    def analyze_stocks_forex_market(self, symbol, asset_type):
        """Análise específica para stocks e forex"""
        try:
            # Usar o serviço de stocks/forex
            comprehensive_data = self.stocks_forex_service.get_comprehensive_analysis(symbol)

            if not comprehensive_data:
                return None

            market_data = comprehensive_data['market_data']
            news_items = comprehensive_data['news']
            sentiment = comprehensive_data['sentiment']

            # Calcular indicadores técnicos se houver dados históricos
            if 'hist_data' in market_data and not market_data['hist_data'].empty:
                df = market_data['hist_data']
                df = self.calculate_indicators(df)

                # Extrair indicadores
                rsi = df['RSI'].iloc[-1] if not df['RSI'].isna().iloc[-1] else 50
                ema50 = df['EMA50'].iloc[-1] if not df['EMA50'].isna().iloc[-1] else market_data['current_price']
                ema200 = df['EMA200'].iloc[-1] if not df['EMA200'].isna().iloc[-1] else market_data['current_price']
                macd = df['MACD'].iloc[-1] if not df['MACD'].isna().iloc[-1] else 0
                macd_signal = df['MACD_Signal'].iloc[-1] if not df['MACD_Signal'].isna().iloc[-1] else 0

                # Suporte e resistência
                support = df['Low'].rolling(window=min(20, len(df))).min().iloc[-1]
                resistance = df['High'].rolling(window=min(20, len(df))).max().iloc[-1]
                volatility = df['Close'].pct_change().std() * 100 if len(df) > 1 else 0
            else:
                # Valores padrão se não houver dados históricos suficientes
                rsi = 50
                ema50 = market_data['current_price']
                ema200 = market_data['current_price']
                macd = 0
                macd_signal = 0
                support = market_data['current_price'] * 0.95
                resistance = market_data['current_price'] * 1.05
                volatility = 0

            # Determinar tendência
            trend = "Bullish" if market_data['current_price'] > ema200 else "Bearish"

            # Preparar dados para análise técnica
            technical_data = {
                'current_price': market_data['current_price'],
                'rsi': rsi,
                'ema50': ema50,
                'ema200': ema200,
                'macd': macd,
                'macd_signal': macd_signal,
                'support': support,
                'resistance': resistance
            }

            # Análise técnica
            technical_recommendation = self.analysis_service.generate_trading_recommendation(technical_data)
            technical_analysis_text = self.analysis_service.format_technical_analysis(
                symbol, technical_data, technical_recommendation
            )

            # Formatar notícias
            news_summary = self.format_stocks_forex_news(news_items, sentiment, symbol)

            # Preparar análise completa
            analysis = {
                'asset_type': asset_type,
                'symbol': market_data['symbol'],
                'coin_name': market_data['name'],
                'current_price': market_data['current_price'],
                'market_cap': market_data.get('market_cap', 0),
                'volume_24h': market_data.get('volume', 0),
                'price_change_24h': market_data.get('price_change_1d', 0),
                'price_change_7d': market_data.get('price_change_5d', 0),
                'price_change_30d': market_data.get('price_change_30d', 0),
                'sector': market_data.get('sector', 'N/A'),
                'industry': market_data.get('industry', 'N/A'),
                'pe_ratio': market_data.get('pe_ratio', 0),
                'dividend_yield': market_data.get('dividend_yield', 0),
                'trend': trend,
                'rsi': rsi,
                'ema50': ema50,
                'ema200': ema200,
                'macd': macd,
                'macd_signal': macd_signal,
                'stoch_rsi': 50,  # Placeholder
                'support': support,
                'resistance': resistance,
                'volatility': volatility,
                'news_data': {'news': news_items, 'sentiment': sentiment, 'total_items': len(news_items)},
                'news_summary': news_summary,
                'technical_analysis': technical_analysis_text,
                'technical_recommendation': technical_recommendation,
                'trading_insights': technical_analysis_text
            }

            return analysis

        except Exception as e:
            print(f"❌ Erro na análise de {asset_type}: {e}")
            return None

    def format_stocks_forex_news(self, news_items, sentiment, symbol):
        """Formatar notícias para stocks/forex"""
        if not news_items:
            return f"📰 Nenhuma notícia recente encontrada para {symbol}"

        sentiment_emoji = {
            'positive': '📈',
            'negative': '📉',
            'neutral': '➡️'
        }

        summary = f"""📰 Notícias Financeiras - {symbol}

{sentiment_emoji.get(sentiment['overall_sentiment'], '➡️')} Sentimento: {sentiment['overall_sentiment'].title()} | 📊 Total: {len(news_items)} notícias
✅ {sentiment['positive_count']} positivas | ❌ {sentiment['negative_count']} negativas | ➡️ {sentiment['neutral_count']} neutras

🔥 TOP 5 NOTÍCIAS DE MAIOR IMPACTO:"""

        for i, news_item in enumerate(news_items[:5]):
            # Calcular impacto em escala de 1-10
            raw_impact = news_item.get('impact_score', 0)
            impact_10_scale = min(10, max(1, int((raw_impact / 20) * 10))) if raw_impact > 0 else 1

            impact_emoji = "🚨" if impact_10_scale >= 8 else "⚡" if impact_10_scale >= 5 else "📌"

            title = news_item['title'][:70] + '...' if len(news_item['title']) > 70 else news_item['title']
            description = news_item.get('description', 'Sem descrição')
            if len(description) > 80:
                description = description[:80] + '...'

            summary += f"""

{i+1}. {impact_emoji} {title}
   💬 {description}
   🔗 {news_item['source']} | 🎯 Impacto: ({impact_10_scale}/10)"""

        summary += f"\n\n🕒 Atualizado: {datetime.now().strftime('%H:%M:%S')}"
        return summary

    def format_analysis_message(self, analysis):
        """Format analysis into a readable message with complete price information"""
        if not analysis:
            return "❌ Erro: Não foi possível obter dados do mercado"

        # Emojis para mudanças de preço
        def get_change_emoji(change):
            if change > 0:
                return "📈" if change > 5 else "🟢"
            elif change < 0:
                return "📉" if change < -5 else "🔴"
            else:
                return "➡️"

        # Determinar emoji de tendência
        trend_emoji = "📈" if analysis['trend'] == "Bullish" else "📉"

        # Determinar emoji de volatilidade
        volatility = analysis['volatility']
        vol_emoji = "🔥" if volatility > 5 else "⚡" if volatility > 2 else "💧"

        # Formatação de números grandes
        def format_large_number(num):
            if num >= 1e9:
                return f"${num/1e9:.2f}B"
            elif num >= 1e6:
                return f"${num/1e6:.2f}M"
            elif num >= 1e3:
                return f"${num/1e3:.2f}K"
            else:
                return f"${num:.2f}"

        # Determinar emoji do tipo de ativo
        asset_emoji = {
            'crypto': '🚀',
            'stock': '📈',
            'forex': '💱'
        }

        asset_type = analysis.get('asset_type', 'crypto')
        emoji = asset_emoji.get(asset_type, '🚀')

        # Informações específicas por tipo de ativo
        if asset_type == 'stock':
            specific_info = f"""
📊 INFORMAÇÕES ESPECÍFICAS (AÇÕES):
┣━ 🏢 Setor: {analysis.get('sector', 'N/A')}
┣━ 🏭 Indústria: {analysis.get('industry', 'N/A')}
┣━ 📊 P/E Ratio: {analysis.get('pe_ratio', 0):.2f}
┗━ 💰 Dividend Yield: {analysis.get('dividend_yield', 0):.2f}%"""
        elif asset_type == 'forex':
            specific_info = f"""
💱 INFORMAÇÕES ESPECÍFICAS (FOREX):
┗━ 🌍 Par de Moedas: {analysis['symbol']}"""
        else:  # crypto
            specific_info = f"""
🚀 INFORMAÇÕES ESPECÍFICAS (CRYPTO):
┗━ 📊 Market Cap: {format_large_number(analysis['market_cap'])}"""

        message = f"""
{emoji} ANÁLISE COMPLETA - {analysis['coin_name']} ({analysis['symbol']}) {emoji}

💰 INFORMAÇÕES DE PREÇO (OBRIGATÓRIAS):
┣━ 💵 Preço Atual: ${analysis['current_price']:.6f}
┣━ 📈 Volume 24h: {format_large_number(analysis['volume_24h'])}
┣━ {get_change_emoji(analysis['price_change_24h'])} 24h: {analysis['price_change_24h']:.2f}%
┣━ {get_change_emoji(analysis['price_change_7d'])} 7d: {analysis['price_change_7d']:.2f}%
┗━ {get_change_emoji(analysis['price_change_30d'])} 30d: {analysis['price_change_30d']:.2f}%
{specific_info}

🎯 NÍVEIS IMPORTANTES (OBRIGATÓRIOS):
┣━ 🔴 Resistência: ${analysis['resistance']:.6f}
┣━ 🟢 Suporte: ${analysis['support']:.6f}
┣━ 📊 EMA50: ${analysis['ema50']:.6f}
┣━ 📈 EMA200: ${analysis['ema200']:.6f}
┗━ 🎯 RSI: {analysis['rsi']:.1f} ({'Sobrecomprado' if analysis['rsi'] > 70 else 'Sobrevendido' if analysis['rsi'] < 30 else 'Neutro'})

{trend_emoji} TENDÊNCIA: {analysis['trend']} | {vol_emoji} VOLATILIDADE: {'Alta' if volatility > 5 else 'Média' if volatility > 2 else 'Baixa'} ({volatility:.1f}%)

{analysis['technical_analysis']}

{analysis['news_summary']}

⚠️ AVISO IMPORTANTE:
Esta análise é apenas para fins educacionais. Sempre faça sua própria pesquisa (DYOR) e negocie com responsabilidade! Nunca invista mais do que pode perder.

🔄 Análise atualizada: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}
"""
        return message