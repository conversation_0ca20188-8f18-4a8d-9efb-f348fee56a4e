import pandas as pd
import numpy as np
from ta.trend import EMAIndicator
from ta.momentum import RSIIndicator, StochasticOscillator
from ta.trend import MACD
from newsapi import NewsApiClient
from datetime import datetime, timedelta
import os
from dotenv import load_dotenv
from .ai_service import AIService
from pycoingecko import CoinGeckoAPI
import tweepy

load_dotenv()

TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
TELEGRAM_CHAT_ID = os.getenv('TELEGRAM_CHAT_ID')

class TradingAnalyzer:
    def __init__(self):
        self.newsapi = NewsApiClient(api_key=os.getenv('NEWS_API_KEY'))
        self.ai_service = AIService()
        self.cg = CoinGeckoAPI()
        self.twitter_client = tweepy.Client(os.getenv('TWITTER_BEARER_TOKEN'))
        
    def get_market_data(self, symbol):
        """Fetch market data for the given symbol from CoinGecko"""
        try:
            # Get Coin ID from symbol
            coin_list = self.cg.get_coins_list()
            coin_id = next((coin['id'] for coin in coin_list if coin['symbol'].upper() == symbol.upper()), None)
            
            if not coin_id:
                print(f"Could not find CoinGecko ID for symbol: {symbol}")
                return None
            print(f"Found CoinGecko ID: {coin_id} for symbol: {symbol}")

            # Fetch historical data (e.g., 30 days daily data)
            # CoinGecko's API for historical data is per-day, so we'll adjust the interval later if needed
            # For now, let's fetch 30 days of data, assuming daily interval is sufficient for basic analysis
            # We can later implement logic for 1h/4h if CoinGecko provides it, or use other APIs.
            # For simplicity, let's fetch daily data for the last 30 days.
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            
            # CoinGecko returns timestamp in milliseconds
            prices = self.cg.get_coin_market_chart_range_by_id(id=coin_id, vs_currency='usd', from_timestamp=start_date.timestamp(), to_timestamp=end_date.timestamp())
            
            if not prices or 'prices' not in prices or not prices['prices']:
                return None
            
            print(f"Raw prices data: {prices.get('prices', [])[:5]}...") # Print first 5 entries
            print(f"Raw total_volumes data: {prices.get('total_volumes', [])[:5]}...") # Print first 5 entries

            df = pd.DataFrame(prices['prices'], columns=['timestamp', 'Close'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            # CoinGecko's simple market chart range only provides price, volume, market_caps. We need to simulate OHLC for TA.
            # For now, we will use the 'Close' price for all OHLC for indicator calculation. This is a simplification
            # and for more accurate TA, a more robust data source with OHLCV data would be needed.
            df['Open'] = df['Close']
            df['High'] = df['Close']
            df['Low'] = df['Close']
            
            # Ensure volume data aligns by timestamp
            volume_df = pd.DataFrame(prices['total_volumes'], columns=['timestamp', 'Volume'])
            volume_df['timestamp'] = pd.to_datetime(volume_df['timestamp'], unit='ms')
            volume_df.set_index('timestamp', inplace=True)
            
            df = df.join(volume_df['Volume'], how='left') # Use left join to keep all price timestamps

            print(f"DataFrame head after processing:\n{df.head()}")
            print(f"DataFrame tail after processing:\n{df.tail()}")
            print(f"DataFrame info:\n{df.info()}")

            return df
        except Exception as e:
            print(f"Error fetching market data from CoinGecko: {e}")
            return None

    def calculate_indicators(self, df):
        """Calculate technical indicators"""
        # RSI
        rsi = RSIIndicator(close=df['Close'], window=14)
        df['RSI'] = rsi.rsi()

        # EMAs
        ema50 = EMAIndicator(close=df['Close'], window=50)
        ema200 = EMAIndicator(close=df['Close'], window=200)
        df['EMA50'] = ema50.ema_indicator()
        df['EMA200'] = ema200.ema_indicator()

        # MACD
        macd = MACD(close=df['Close'])
        df['MACD'] = macd.macd()
        df['MACD_Signal'] = macd.macd_signal()

        # Stochastic RSI
        stoch_rsi = StochasticOscillator(high=df['High'], low=df['Low'], close=df['Close'])
        df['Stoch_RSI'] = stoch_rsi.stoch()

        return df

    def get_news(self, symbol):
        """Fetch relevant news for the symbol"""
        try:
            # Remove USDT suffix if present
            search_term = symbol.replace('USDT', '')
            news = self.newsapi.get_everything(
                q=search_term,
                language='en',
                sort_by='relevancy',
                page_size=5
            )
            return news['articles']
        except Exception as e:
            print(f"Error fetching news: {e}")
            return []

    def get_tweets(self, symbol, count=10):
        """Fetch recent tweets for the given symbol"""
        try:
            query = f"#{symbol} lang:en -is:retweet"
            response = self.twitter_client.search_recent_tweets(query=query, tweet_fields=["created_at", "text"], max_results=count)
            tweets = [tweet.text for tweet in response.data]
            return tweets
        except Exception as e:
            print(f"Error fetching tweets: {e}")
            return []

    def analyze_market(self, symbol):
        """Perform complete market analysis"""
        # Get market data
        df = self.get_market_data(symbol)
        
        if df is None:
            return None

        # Calculate indicators
        df = self.calculate_indicators(df)

        # Get latest values
        current_price = df['Close'].iloc[-1]
        rsi = df['RSI'].iloc[-1]
        ema50 = df['EMA50'].iloc[-1]
        ema200 = df['EMA200'].iloc[-1]
        macd = df['MACD'].iloc[-1]
        macd_signal = df['MACD_Signal'].iloc[-1]
        stoch_rsi = df['Stoch_RSI'].iloc[-1]

        # Determine trend
        trend = "Bullish" if current_price > ema200 else "Bearish"
        
        # Calculate support and resistance
        support = df['Low'].rolling(window=20).min().iloc[-1]
        resistance = df['High'].rolling(window=20).max().iloc[-1]

        # Get news
        news = self.get_news(symbol)
        
        # Get tweets
        tweets = self.get_tweets(symbol)

        # Prepare technical analysis text
        technical_analysis = f"""
Current Price: ${current_price:.2f}
Trend: {trend}
RSI: {rsi:.1f}
EMA50: ${ema50:.2f}
EMA200: ${ema200:.2f}
MACD: {macd:.2f}
MACD Signal: {macd_signal:.2f}
Stochastic RSI: {stoch_rsi:.1f}
Support: ${support:.2f}
Resistance: ${resistance:.2f}
"""

        # Get AI analysis
        news_analysis = self.ai_service.analyze_news(news)
        twitter_sentiment = self.ai_service.analyze_twitter_sentiment(tweets)
        sentiment_analysis = self.ai_service.analyze_market_sentiment(technical_analysis)
        trading_insights = self.ai_service.generate_trading_insights(
            symbol,
            technical_analysis,
            news_analysis,
            sentiment_analysis,
            twitter_sentiment
        )

        # Prepare analysis
        analysis = {
            'symbol': symbol,
            'current_price': current_price,
            'trend': trend,
            'rsi': rsi,
            'ema50': ema50,
            'ema200': ema200,
            'macd': macd,
            'macd_signal': macd_signal,
            'stoch_rsi': stoch_rsi,
            'support': support,
            'resistance': resistance,
            'news': news,
            'tweets': tweets,
            'volume': df['Volume'].iloc[-1],
            'volatility': df['Close'].pct_change().std() * 100,
            'news_analysis': news_analysis,
            'sentiment_analysis': sentiment_analysis,
            'twitter_sentiment': twitter_sentiment,
            'trading_insights': trading_insights
        }

        return analysis

    def format_analysis_message(self, analysis):
        """Format analysis into a readable message"""
        if not analysis:
            return "❌ Error: Could not fetch market data"

        message = f"""
📊 Daily {analysis['symbol']} Analysis 📊

{analysis['trading_insights']}

News Analysis 📰
{analysis['news_analysis']}

Market Sentiment Analysis 📈
{analysis['sentiment_analysis']}

Twitter Sentiment Analysis 🐦
{analysis['twitter_sentiment']}

Technical Indicators 📊
• RSI: {analysis['rsi']:.1f} ({'Overbought' if analysis['rsi'] > 70 else 'Oversold' if analysis['rsi'] < 30 else 'Neutral'})
• MACD: {'Bullish' if analysis['macd'] > analysis['macd_signal'] else 'Bearish'} crossover
• EMA50: ${analysis['ema50']:.2f}
• EMA200: ${analysis['ema200']:.2f}
• Support: ${analysis['support']:.2f}
• Resistance: ${analysis['resistance']:.2f}

Volume & Volatility 📊
• Volume: {'Increasing' if analysis['volume'] > analysis['volume'] * 1.1 else 'Normal'}
• Volatility: {'High' if analysis['volatility'] > 5 else 'Medium' if analysis['volatility'] > 2 else 'Low'}

Remember: This analysis is for educational purposes only. Always do your own research and trade responsibly! 🚀
"""
        return message 