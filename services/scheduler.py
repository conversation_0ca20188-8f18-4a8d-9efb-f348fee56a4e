import time
from apscheduler.schedulers.background import BackgroundScheduler
from services.solana import fetch_new_pools, filter_pools
from services.telegram import send_telegram_alert
from services.db import save_pool
from typing import List
import logging

logging.basicConfig(level=logging.INFO)

RETRY_LIMIT = 3
RETRY_DELAY = 5  # seconds

class Scheduler:
    def __init__(self):
        self.scheduler = BackgroundScheduler()
        
    def process_pools(self):
        """
        Fetch, filter, alert, and store new pools with retry logic.
        """
        for attempt in range(RETRY_LIMIT):
            try:
                pools = fetch_new_pools()
                valid_pools = filter_pools(pools)
                for pool in valid_pools:
                    save_pool(pool)
                    send_telegram_alert(pool)
                logging.info(f"Processed {len(valid_pools)} valid pools.")
                break
            except Exception as e:
                logging.error(f"Error processing pools: {e}")
                if attempt < RETRY_LIMIT - 1:
                    time.sleep(RETRY_DELAY)
                else:
                    logging.error("Max retries reached. Skipping this cycle.")

    def start(self):
        """Start the scheduler"""
        self.scheduler.add_job(self.process_pools, 'interval', minutes=5)
        self.scheduler.start()
        logging.info("Scheduler started successfully")

    def stop(self):
        """Stop the scheduler"""
        self.scheduler.shutdown()
        logging.info("Scheduler stopped successfully") 