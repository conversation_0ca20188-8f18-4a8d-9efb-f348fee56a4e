import os
from pymongo import MongoClient
from dotenv import load_dotenv

load_dotenv()

MONGO_URI = os.getenv('MONGO_URI')
client = MongoClient(MONGO_URI)
db = client['liquidity_sniper']
pools_collection = db['pools']

def save_pool(pool: dict):
    """
    Save a discovered pool to MongoDB.
    """
    pools_collection.update_one(
        {"address": pool["address"]},
        {"$set": pool},
        upsert=True
    ) 