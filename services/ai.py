import os
import requests
from dotenv import load_dotenv
from typing import Optional

load_dotenv()

# Configure DeepSeek AI
DEEPSEEK_API_KEY = os.getenv('GOOGLE_API_KEY')  # Reusing the env var name for compatibility
DEEPSEEK_API_URL = "https://api.deepseek.com/v1/chat/completions"

def call_deepseek(prompt: str) -> str:
    """
    Make a call to DeepSeek API and return the response.
    """
    try:
        headers = {
            "Authorization": f"Bearer {DEEPSEEK_API_KEY}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "deepseek-chat-v3-0324",
            "messages": [
                {"role": "system", "content": "You are a professional crypto analyst. Keep responses concise and focused on key metrics."},
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.7,
            "max_tokens": 150
        }
        
        response = requests.post(DEEPSEEK_API_URL, headers=headers, json=data, timeout=10)
        response.raise_for_status()
        return response.json()['choices'][0]['message']['content'].strip()
    except Exception as e:
        print(f"Error calling DeepSeek API: {e}")
        return "Unable to analyze at this time."

def analyze_token(token_info: dict) -> str:
    """
    Analyze a token using DeepSeek AI and return insights.
    """
    prompt = f"""
    Analyze this Solana token and provide a brief, professional assessment:
    Name: {token_info.get('name')}
    Liquidity: ${token_info.get('liquidity'):,}
    Holders: {token_info.get('holders', 'N/A')}
    24h Volume: ${token_info.get('volume'):,}
    
    Provide a one-line risk assessment focusing on key metrics.
    """
    return call_deepseek(prompt)

def get_ai_strategy() -> str:
    """
    Get AI-generated strategy for memecoin sniping.
    """
    prompt = """
    Provide a brief, professional strategy for identifying promising memecoin opportunities on Solana.
    Focus on key metrics and risk factors to consider.
    Keep it concise and actionable.
    """
    return call_deepseek(prompt)

def analyze_pool_risk(pool_info: dict) -> str:
    """
    Analyze pool risk using DeepSeek AI.
    """
    prompt = f"""
    Analyze this Solana liquidity pool and provide a brief risk assessment:
    Pool: {pool_info.get('name')}
    Liquidity: ${pool_info.get('liquidity'):,}
    Holders: {pool_info.get('holders', 'N/A')}
    24h Volume: ${pool_info.get('volume'):,}
    
    Focus on liquidity depth, holder distribution, and volume patterns.
    Keep it concise and professional.
    """
    return call_deepseek(prompt) 