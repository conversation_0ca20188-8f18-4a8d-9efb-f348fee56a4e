from typing import Dict

DEFAULT_SETTINGS = {
    'min_liquidity': 500,
    'min_holders': 50,
    'keywords': ['INU', 'DOGE', 'PEPE', 'MEME'],
    'min_volume_24h': 1000
}

def get_user_settings(user_id: str) -> Dict:
    """
    Get user settings (returns defaults for now).
    """
    settings = DEFAULT_SETTINGS.copy()
    settings['user_id'] = user_id
    return settings

def update_user_settings(user_id: str, new_settings: Dict) -> Dict:
    """
    Update user settings (in-memory only).
    """
    settings = get_user_settings(user_id)
    settings.update(new_settings)
    return settings

def get_all_settings() -> Dict[str, Dict]:
    """
    Get settings for all users (returns only default for now).
    """
    return {"default": DEFAULT_SETTINGS.copy()} 