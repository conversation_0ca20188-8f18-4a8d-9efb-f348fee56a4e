import requests
import pandas as pd
from datetime import datetime, timedelta
import yfinance as yf
from bs4 import BeautifulSoup
import feedparser
from textblob import TextBlob
import time

class StocksForexService:
    """Serviço para análise de stocks e forex usando APIs gratuitas"""
    
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        # Mapeamento de símbolos forex
        self.forex_pairs = {
            'EURUSD': 'EURUSD=X',
            'GBPUSD': 'GBPUSD=X',
            'USDJPY': 'USDJPY=X',
            'USDCHF': 'USDCHF=X',
            'AUDUSD': 'AUDUSD=X',
            'USDCAD': 'USDCAD=X',
            'NZDUSD': 'NZDUSD=X',
            'EURJPY': 'EURJPY=X',
            'GBPJPY': 'GBPJPY=X',
            'EURGBP': 'EURGBP=X'
        }

    def detect_asset_type(self, symbol):
        """Detecta se é crypto, stock ou forex"""
        symbol_upper = symbol.upper()
        
        # Verificar se é forex
        if symbol_upper in self.forex_pairs:
            return 'forex'
        
        # Verificar se é crypto (símbolos comuns)
        crypto_symbols = ['BTC', 'ETH', 'SOL', 'ADA', 'DOT', 'LINK', 'UNI', 'AVAX', 'MATIC', 'ATOM']
        if symbol_upper in crypto_symbols:
            return 'crypto'
        
        # Por padrão, assumir que é stock
        return 'stock'

    def get_stock_data(self, symbol):
        """Busca dados de ações usando yfinance (gratuito)"""
        try:
            # Símbolos de ações populares para testar primeiro
            popular_stocks = {
                'AAPL': 'AAPL',
                'GOOGL': 'GOOGL',
                'MSFT': 'MSFT',
                'TSLA': 'TSLA',
                'AMZN': 'AMZN',
                'META': 'META',
                'NVDA': 'NVDA',
                'NFLX': 'NFLX'
            }

            # Tentar diferentes sufixos para ações internacionais
            suffixes = ['', '.SA', '.L', '.PA', '.DE', '.TO', '.AS', '.MI']

            # Se é uma ação popular, tentar primeiro sem sufixo
            if symbol.upper() in popular_stocks:
                suffixes = [''] + suffixes

            for suffix in suffixes:
                try:
                    ticker_symbol = symbol.upper() + suffix
                    ticker = yf.Ticker(ticker_symbol)

                    # Buscar dados históricos primeiro (mais confiável)
                    hist = ticker.history(period="1mo")

                    if not hist.empty:
                        print(f"✅ Dados históricos encontrados para {ticker_symbol}")

                        # Buscar informações básicas (pode falhar às vezes)
                        try:
                            info = ticker.info
                        except:
                            info = {}

                        current_price = hist['Close'].iloc[-1]

                        # Calcular mudanças de preço
                        price_change_1d = ((current_price - hist['Close'].iloc[-2]) / hist['Close'].iloc[-2] * 100) if len(hist) > 1 else 0
                        price_change_5d = ((current_price - hist['Close'].iloc[-6]) / hist['Close'].iloc[-6] * 100) if len(hist) > 5 else 0
                        price_change_30d = ((current_price - hist['Close'].iloc[0]) / hist['Close'].iloc[0] * 100) if len(hist) > 0 else 0

                        return {
                            'symbol': ticker_symbol,
                            'name': info.get('longName', ticker_symbol),
                            'current_price': current_price,
                            'market_cap': info.get('marketCap', 0),
                            'volume': hist['Volume'].iloc[-1] if not hist['Volume'].empty else 0,
                            'price_change_1d': price_change_1d,
                            'price_change_5d': price_change_5d,
                            'price_change_30d': price_change_30d,
                            'sector': info.get('sector', 'Technology'),
                            'industry': info.get('industry', 'Software'),
                            'pe_ratio': info.get('trailingPE', 0),
                            'dividend_yield': info.get('dividendYield', 0) * 100 if info.get('dividendYield') else 0,
                            'hist_data': hist,
                            'asset_type': 'stock'
                        }
                except Exception as e:
                    print(f"⚠️ Erro ao tentar {ticker_symbol}: {e}")
                    continue

            print(f"❌ Não foi possível encontrar dados para {symbol}")
            return None

        except Exception as e:
            print(f"❌ Erro ao buscar dados de ações: {e}")
            return None

    def get_forex_data(self, symbol):
        """Busca dados de forex usando yfinance e APIs alternativas"""
        try:
            symbol_upper = symbol.upper()

            # Tentar diferentes formatos de símbolos forex
            possible_symbols = []

            if symbol_upper in self.forex_pairs:
                possible_symbols.append(self.forex_pairs[symbol_upper])

            # Adicionar formatos alternativos
            possible_symbols.extend([
                f"{symbol_upper}=X",
                f"{symbol_upper[:3]}{symbol_upper[3:]}=X",
                symbol_upper
            ])

            for yf_symbol in possible_symbols:
                try:
                    print(f"🔍 Tentando buscar dados para {yf_symbol}...")
                    ticker = yf.Ticker(yf_symbol)

                    # Buscar dados históricos
                    hist = ticker.history(period="5d")  # Período menor para forex

                    if not hist.empty:
                        current_price = hist['Close'].iloc[-1]

                        # Calcular mudanças
                        price_change_1d = ((current_price - hist['Close'].iloc[-2]) / hist['Close'].iloc[-2] * 100) if len(hist) > 1 else 0
                        price_change_5d = ((current_price - hist['Close'].iloc[0]) / hist['Close'].iloc[0] * 100) if len(hist) > 0 else 0
                        price_change_30d = price_change_5d  # Usar 5d como aproximação para 30d

                        print(f"✅ Dados forex encontrados para {yf_symbol}")

                        return {
                            'symbol': yf_symbol,
                            'name': f'{symbol_upper} Exchange Rate',
                            'current_price': current_price,
                            'volume': hist['Volume'].iloc[-1] if not hist['Volume'].empty else 1000000,  # Volume padrão para forex
                            'price_change_1d': price_change_1d,
                            'price_change_5d': price_change_5d,
                            'price_change_30d': price_change_30d,
                            'hist_data': hist,
                            'asset_type': 'forex'
                        }
                except Exception as e:
                    print(f"⚠️ Erro ao tentar {yf_symbol}: {e}")
                    continue

            # Se não conseguir dados do yfinance, tentar API alternativa gratuita
            try:
                print(f"🔍 Tentando API alternativa para {symbol_upper}...")
                # Usar exchangerate-api.com (gratuito)
                if len(symbol_upper) == 6:  # EURUSD format
                    base_currency = symbol_upper[:3]
                    target_currency = symbol_upper[3:]

                    url = f"https://api.exchangerate-api.com/v4/latest/{base_currency}"
                    response = requests.get(url, timeout=10)

                    if response.status_code == 200:
                        data = response.json()
                        if target_currency in data['rates']:
                            current_price = data['rates'][target_currency]

                            print(f"✅ Dados forex encontrados via API alternativa")

                            return {
                                'symbol': symbol_upper,
                                'name': f'{symbol_upper} Exchange Rate',
                                'current_price': current_price,
                                'volume': 1000000,  # Volume padrão
                                'price_change_1d': 0,  # Não disponível na API gratuita
                                'price_change_5d': 0,
                                'price_change_30d': 0,
                                'hist_data': pd.DataFrame(),  # DataFrame vazio
                                'asset_type': 'forex'
                            }
            except Exception as e:
                print(f"⚠️ Erro na API alternativa: {e}")

            print(f"❌ Dados forex não encontrados para {symbol}")
            return None

        except Exception as e:
            print(f"❌ Erro ao buscar dados forex: {e}")
            return None

    def get_financial_news(self, symbol, asset_type, limit=5):
        """Busca notícias financeiras de fontes gratuitas"""
        try:
            news_items = []
            
            # Yahoo Finance RSS
            if asset_type == 'stock':
                rss_urls = [
                    "https://feeds.finance.yahoo.com/rss/2.0/headline",
                    "https://finance.yahoo.com/rss/topstories"
                ]
            elif asset_type == 'forex':
                rss_urls = [
                    "https://feeds.finance.yahoo.com/rss/2.0/headline",
                    "https://www.forexfactory.com/rss.php"
                ]
            else:
                rss_urls = ["https://feeds.finance.yahoo.com/rss/2.0/headline"]
            
            # Palavras-chave de alto impacto para finanças
            high_impact_keywords = [
                'breaking', 'earnings', 'dividend', 'merger', 'acquisition',
                'fed', 'interest rate', 'inflation', 'gdp', 'unemployment',
                'recession', 'bull market', 'bear market', 'crash', 'surge',
                'ipo', 'bankruptcy', 'lawsuit', 'regulation', 'approval'
            ]
            
            for rss_url in rss_urls:
                try:
                    feed = feedparser.parse(rss_url)
                    
                    for entry in feed.entries[:20]:
                        title = entry.title.lower()
                        description = entry.get('description', '').lower()
                        
                        # Calcular score de relevância
                        relevance_score = 0
                        impact_score = 0
                        
                        # Relevância para o símbolo
                        if symbol.lower() in title or symbol.lower() in description:
                            relevance_score += 10
                        
                        # Score de impacto
                        for keyword in high_impact_keywords:
                            if keyword in title:
                                impact_score += 5
                            elif keyword in description:
                                impact_score += 2
                        
                        total_score = relevance_score + impact_score
                        
                        if total_score > 0 or len(news_items) < 3:  # Incluir pelo menos 3 notícias
                            # Criar resumo curto
                            description_text = entry.get('description', '')
                            if description_text:
                                clean_desc = BeautifulSoup(description_text, 'html.parser').get_text()
                                short_summary = clean_desc[:100] + '...' if len(clean_desc) > 100 else clean_desc
                            else:
                                short_summary = "Notícia financeira relevante"
                            
                            news_items.append({
                                'title': entry.title,
                                'description': short_summary,
                                'url': entry.link,
                                'published_at': entry.get('published', ''),
                                'source': 'Yahoo Finance',
                                'impact_score': total_score
                            })
                
                except Exception as e:
                    print(f"Erro ao processar RSS {rss_url}: {e}")
                    continue
            
            # Ordenar por impacto e retornar top 5
            news_items.sort(key=lambda x: x['impact_score'], reverse=True)
            return news_items[:limit]
            
        except Exception as e:
            print(f"Erro ao buscar notícias financeiras: {e}")
            return []

    def analyze_sentiment(self, news_items):
        """Análise de sentimento das notícias financeiras"""
        if not news_items:
            return {
                'overall_sentiment': 'neutral',
                'positive_count': 0,
                'negative_count': 0,
                'neutral_count': 0,
                'sentiment_score': 0.0
            }
        
        sentiments = []
        positive_count = 0
        negative_count = 0
        neutral_count = 0
        
        for item in news_items:
            text = f"{item.get('title', '')} {item.get('description', '')}"
            
            try:
                blob = TextBlob(text)
                polarity = blob.sentiment.polarity
                
                sentiments.append(polarity)
                
                if polarity > 0.1:
                    positive_count += 1
                elif polarity < -0.1:
                    negative_count += 1
                else:
                    neutral_count += 1
                    
            except Exception as e:
                print(f"Erro na análise de sentimento: {e}")
                neutral_count += 1
        
        avg_sentiment = sum(sentiments) / len(sentiments) if sentiments else 0.0
        
        if avg_sentiment > 0.1:
            overall_sentiment = 'positive'
        elif avg_sentiment < -0.1:
            overall_sentiment = 'negative'
        else:
            overall_sentiment = 'neutral'
        
        return {
            'overall_sentiment': overall_sentiment,
            'positive_count': positive_count,
            'negative_count': negative_count,
            'neutral_count': neutral_count,
            'sentiment_score': avg_sentiment
        }

    def get_comprehensive_analysis(self, symbol):
        """Análise completa para stocks/forex"""
        print(f"🔍 Analisando {symbol}...")
        
        # Detectar tipo de ativo
        asset_type = self.detect_asset_type(symbol)
        print(f"📊 Tipo detectado: {asset_type}")
        
        # Buscar dados do mercado
        if asset_type == 'forex':
            market_data = self.get_forex_data(symbol)
        else:  # stock
            market_data = self.get_stock_data(symbol)
        
        if not market_data:
            return None
        
        # Buscar notícias
        news_items = self.get_financial_news(symbol, asset_type)
        
        # Análise de sentimento
        sentiment_analysis = self.analyze_sentiment(news_items)
        
        return {
            'market_data': market_data,
            'news': news_items,
            'sentiment': sentiment_analysis,
            'asset_type': asset_type
        }
