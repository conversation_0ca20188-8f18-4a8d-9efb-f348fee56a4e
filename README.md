# CriptoTipic4 - Crypto Trading Bot 🚀

A sophisticated Telegram bot for cryptocurrency analysis and trading insights, powered by AI and real-time market data.

## Features ✨

- 📊 **Real-time Market Analysis** - Live price data, technical indicators (RSI, MACD, EMAs)
- 🤖 **AI-Powered Insights** - Intelligent trading recommendations and market sentiment analysis
- 📰 **News Analysis** - Latest cryptocurrency news with AI-powered impact assessment
- 🐦 **Social Sentiment** - Twitter sentiment analysis for market trends
- 📈 **Technical Indicators** - RSI, MACD, EMA50/200, Support/Resistance levels
- ⚡ **Instant Analysis** - Just send a crypto symbol (SOL, BTC, ETH) to get analysis
- 🔔 **Telegram Integration** - Easy-to-use interface through Telegram

## Quick Start 🚀

### 1. Clone and Setup
```bash
git clone <your-repo-url>
cd CriptoTipica
pip install -r requirements.txt
```

### 2. Configure Environment
Copy `.env.example` to `.env` and configure your API keys:

```bash
cp env.example .env
```

Edit `.env` with your API keys:
```env
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id
OPENROUTER_API_KEY=your_openrouter_api_key
NEWS_API_KEY=your_newsapi_key (optional)
TWITTER_BEARER_TOKEN=your_twitter_token (optional)
```

### 3. Get API Keys

#### Required APIs:
- **Telegram Bot**: Go to [@BotFather](https://t.me/botfather) on Telegram
- **OpenRouter AI**: Get free credits at [openrouter.ai](https://openrouter.ai/)

#### Optional APIs:
- **News API**: Free tier at [newsapi.org](https://newsapi.org/)
- **Twitter API**: Developer account at [developer.twitter.com](https://developer.twitter.com/)

### 4. Test and Run
```bash
# Test the bot
python test_bot.py

# Run the bot
python app.py
```

## Usage 💬

### Telegram Commands:
- `/start` - Welcome message and help
- `/analyze SOL` - Detailed analysis for Solana
- `/help` - Show help information

### Quick Analysis:
Just send any crypto symbol:
- `SOL` - Solana analysis
- `BTC` - Bitcoin analysis
- `ETH` - Ethereum analysis

## API Configuration 🔧

### Getting Telegram Bot Token:
1. Message [@BotFather](https://t.me/botfather) on Telegram
2. Create new bot: `/newbot`
3. Follow instructions and copy the token
4. Add token to `TELEGRAM_BOT_TOKEN` in `.env`

### Getting Chat ID:
1. Send a message to your bot
2. Visit: `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
3. Find the chat ID in the response
4. Add to `TELEGRAM_CHAT_ID` in `.env`

### Getting OpenRouter API Key:
1. Sign up at [openrouter.ai](https://openrouter.ai/)
2. Get free credits (usually $1-5 for new users)
3. Create API key in dashboard
4. Add to `OPENROUTER_API_KEY` in `.env`

## Troubleshooting 🔧

### Common Issues:

1. **"Payment Required" Error**:
   - Add credits to your OpenRouter account
   - The bot will work with limited features without AI

2. **"Unable to analyze" Messages**:
   - Check your API keys in `.env`
   - Ensure you have credits in OpenRouter
   - Run `python test_bot.py` to diagnose

3. **Import Errors**:
   - Install dependencies: `pip install -r requirements.txt`
   - For M1/M2 Macs: `pip install --force-reinstall pandas numpy`

### Test Your Setup:
```bash
python test_bot.py
```

This will check:
- ✅ Environment configuration
- ✅ API connectivity
- ✅ Market data fetching
- ✅ Message formatting

## Features Breakdown 📊

### Technical Analysis:
- **RSI (Relative Strength Index)** - Momentum indicator
- **MACD** - Trend following momentum indicator
- **EMA50/200** - Exponential moving averages
- **Support/Resistance** - Key price levels
- **Volume Analysis** - Trading volume patterns

### AI Analysis (requires credits):
- **News Impact Assessment** - How news affects price
- **Market Sentiment** - Overall market mood
- **Trading Recommendations** - Entry/exit suggestions
- **Risk Assessment** - Risk level evaluation

### Data Sources:
- **CoinGecko** - Price and market data (free)
- **NewsAPI** - Cryptocurrency news (optional)
- **Twitter API** - Social sentiment (optional)
- **OpenRouter** - AI analysis (requires credits)

## System Service (Optional) 🔄

To run the bot as a system service:

```bash
python setup_bot.py
sudo cp criptotipic4.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable criptotipic4
sudo systemctl start criptotipic4
```

## Support 💬

If you encounter issues:
1. Run `python test_bot.py` for diagnostics
2. Check the console output for error messages
3. Ensure all API keys are correctly configured
4. Verify you have credits in OpenRouter for AI features

## Disclaimer ⚠️

This bot is for educational purposes only. Always do your own research (DYOR) and never invest more than you can afford to lose. Cryptocurrency trading involves significant risk.

---

**Happy Trading! 🚀**
