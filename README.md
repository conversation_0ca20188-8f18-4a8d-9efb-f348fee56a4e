# CriptoTipic4 - Bot de Trading Crypto 🚀

Um bot Telegram sofisticado para análise de criptomoedas e insights de trading, usando **APENAS APIs GRATUITAS**!

## Características ✨

- 📊 **Análise de Mercado em Tempo Real** - Dados de preço ao vivo, indicadores técnicos (RSI, MACD, EMAs)
- 🧠 **Análise Técnica Inteligente** - Recomendações de trading baseadas em regras técnicas
- 📰 **Notícias Multi-Fonte** - CoinGecko, CoinTelegraph e Twitter (X)
- 🎯 **Análise de Sentimento** - Análise automática de sentimento das notícias
- 📈 **Indicadores Técnicos** - RSI, MACD, EMA50/200, Suporte/Resistência
- ⚡ **Análise Instantânea** - Envie um símbolo crypto (SOL, BTC, ETH) para análise
- 🔔 **Integração Telegram** - Interface fácil de usar via Telegram
- 💰 **100% GRATUITO** - Sem necessidade de APIs pagas!

## Quick Start 🚀

### 1. <PERSON><PERSON> and Setup
```bash
git clone <your-repo-url>
cd CriptoTipica
pip install -r requirements.txt
```

### 2. Configure Environment
Copie `.env.example` para `.env` e configure suas chaves de API:

```bash
cp env.example .env
```

Edite `.env` com suas chaves de API:
```env
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id
TWITTER_BEARER_TOKEN=your_twitter_token (opcional)
```

### 3. Obter Chaves de API

#### APIs Obrigatórias:
- **Telegram Bot**: Vá para [@BotFather](https://t.me/botfather) no Telegram

#### APIs Opcionais (para mais notícias):
- **Twitter API**: Conta de desenvolvedor em [developer.twitter.com](https://developer.twitter.com/)

### 4. Test and Run
```bash
# Test the bot
python test_bot.py

# Run the bot
python app.py
```

## Como Usar 💬

### Comandos do Telegram:
- `/start` - Mensagem de boas-vindas e ajuda
- `/analyze SOL` - Análise detalhada para Solana
- `/help` - Mostrar informações de ajuda

### Análise Rápida:
Envie qualquer símbolo de crypto:
- `SOL` - Análise do Solana
- `BTC` - Análise do Bitcoin
- `ETH` - Análise do Ethereum

## API Configuration 🔧

### Getting Telegram Bot Token:
1. Message [@BotFather](https://t.me/botfather) on Telegram
2. Create new bot: `/newbot`
3. Follow instructions and copy the token
4. Add token to `TELEGRAM_BOT_TOKEN` in `.env`

### Getting Chat ID:
1. Send a message to your bot
2. Visit: `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
3. Find the chat ID in the response
4. Add to `TELEGRAM_CHAT_ID` in `.env`

## Solução de Problemas 🔧

### Problemas Comuns:

1. **Erro "Unable to analyze"**:
   - Verifique suas chaves de API no `.env`
   - Execute `python test_bot.py` para diagnóstico

2. **Poucas notícias encontradas**:
   - Configure a API do Twitter para mais notícias sociais
   - O bot funciona mesmo sem APIs opcionais

3. **Import Errors**:
   - Install dependencies: `pip install -r requirements.txt`
   - For M1/M2 Macs: `pip install --force-reinstall pandas numpy`

### Test Your Setup:
```bash
python test_bot.py
```

This will check:
- ✅ Environment configuration
- ✅ API connectivity
- ✅ Market data fetching
- ✅ Message formatting

## Detalhamento das Funcionalidades 📊

### Análise Técnica:
- **RSI (Relative Strength Index)** - Indicador de momentum
- **MACD** - Indicador de tendência e momentum
- **EMA50/200** - Médias móveis exponenciais
- **Suporte/Resistência** - Níveis de preço importantes
- **Análise de Volume** - Padrões de volume de negociação

### Análise de Notícias (100% Gratuita):
- **Avaliação de Impacto** - Como as notícias afetam o preço
- **Sentimento de Mercado** - Humor geral do mercado
- **Recomendações de Trading** - Sugestões de entrada/saída
- **Avaliação de Risco** - Avaliação do nível de risco

### Fontes de Dados:
- **CoinGecko** - Dados de preço e mercado (gratuito)
- **CoinTelegraph** - Notícias de criptomoedas (gratuito via RSS)
- **Twitter API** - Sentimento social (opcional)
- **TextBlob** - Análise de sentimento (gratuito)

## System Service (Optional) 🔄

To run the bot as a system service:

```bash
python setup_bot.py
sudo cp criptotipic4.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable criptotipic4
sudo systemctl start criptotipic4
```

## Support 💬

If you encounter issues:
1. Run `python test_bot.py` for diagnostics
2. Check the console output for error messages
3. Ensure all API keys are correctly configured
4. Verify you have credits in OpenRouter for AI features

## Disclaimer ⚠️

This bot is for educational purposes only. Always do your own research (DYOR) and never invest more than you can afford to lose. Cryptocurrency trading involves significant risk.

---

**Happy Trading! 🚀**
