import os
from flask import Flask, jsonify, request
from dotenv import load_dotenv
from services.telegram import TelegramBot
from services.scheduler import Scheduler

# Load environment variables
load_dotenv()

app = Flask(__name__)
telegram_bot = TelegramBot()
scheduler = Scheduler()

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({"status": "healthy"})

def main():
    # Start the Telegram bot no thread principal
    telegram_bot.run()
    # Start the scheduler
    scheduler.start()
    # Run the Flask app em outra porta
    app.run(host='0.0.0.0', port=5001)

if __name__ == '__main__':
    main() 