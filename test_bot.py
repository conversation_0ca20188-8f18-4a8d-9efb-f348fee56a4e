#!/usr/bin/env python3
"""
Test script for CriptoTipic4 bot functionality
"""

import os
import sys
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.trading import TradingAnalyzer
from services.news_service import CryptoNewsService
from services.analysis_service import TechnicalAnalysisService

def test_environment():
    """Test if environment variables are properly loaded"""
    load_dotenv()
    
    print("🔍 Testing Environment Configuration...")
    
    # Check required environment variables
    required_vars = [
        'TELEGRAM_BOT_TOKEN',
        'TELEGRAM_CHAT_ID'
    ]

    optional_vars = [
        'TWITTER_BEARER_TOKEN'
    ]
    
    print("\n✅ Required Variables:")
    for var in required_vars:
        value = os.getenv(var)
        if value and value != f'your_{var.lower()}_here':
            print(f"  {var}: ✓ Configured")
        else:
            print(f"  {var}: ❌ Missing or not configured")
    
    print("\n⚠️  Optional Variables:")
    for var in optional_vars:
        value = os.getenv(var)
        if value and value != f'your_{var.lower()}_here':
            print(f"  {var}: ✓ Configured")
        else:
            print(f"  {var}: ❌ Missing (some features will be limited)")

def test_news_service():
    """Test news service functionality"""
    print("\n📰 Testing News Service...")

    try:
        news_service = CryptoNewsService()

        # Test CoinGecko news
        print("  Testing CoinGecko news...")
        coingecko_news = news_service.get_coingecko_news('SOL', 2)
        print(f"    ✅ CoinGecko: {len(coingecko_news)} items")

        # Test CoinTelegraph news
        print("  Testing CoinTelegraph news...")
        cointelegraph_news = news_service.get_cointelegraph_news('bitcoin', 2)
        print(f"    ✅ CoinTelegraph: {len(cointelegraph_news)} items")

        # Test comprehensive news
        print("  Testing comprehensive news...")
        comprehensive_news = news_service.get_comprehensive_news('SOL', 2)
        print(f"    ✅ Total news: {comprehensive_news['total_items']} items")
        print(f"    ✅ Sentiment: {comprehensive_news['sentiment']['overall_sentiment']}")

        print("  ✅ News Service tests completed")

    except Exception as e:
        print(f"  ❌ News Service error: {e}")

def test_analysis_service():
    """Test technical analysis service"""
    print("\n📊 Testing Analysis Service...")

    try:
        analysis_service = TechnicalAnalysisService()

        # Test with mock data
        mock_data = {
            'current_price': 150.25,
            'rsi': 65.5,
            'ema50': 145.30,
            'ema200': 140.80,
            'macd': 2.5,
            'macd_signal': 2.1,
            'support': 145.00,
            'resistance': 155.00
        }

        recommendation = analysis_service.generate_trading_recommendation(mock_data)
        print(f"    ✅ Recommendation: {recommendation['recommendation']['action']}")
        print(f"    ✅ Confidence: {recommendation['recommendation']['confidence']}")

        formatted_analysis = analysis_service.format_technical_analysis('SOL', mock_data, recommendation)
        print(f"    ✅ Analysis formatted: {len(formatted_analysis)} characters")

        print("  ✅ Analysis Service tests completed")

    except Exception as e:
        print(f"  ❌ Analysis Service error: {e}")

def test_trading_analyzer():
    """Test trading analyzer functionality"""
    print("\n📊 Testing Trading Analyzer...")
    
    try:
        analyzer = TradingAnalyzer()
        
        # Test with SOL (Solana)
        print("  Testing SOL analysis...")
        analysis = analyzer.analyze_market('SOL')
        
        if analysis:
            print("  ✅ Market analysis successful")
            print(f"    Symbol: {analysis['symbol']}")
            print(f"    Current Price: ${analysis['current_price']:.2f}")
            print(f"    RSI: {analysis['rsi']:.1f}")
            print(f"    Trend: {analysis['trend']}")
        else:
            print("  ❌ Market analysis failed")
            
    except Exception as e:
        print(f"  ❌ Trading Analyzer error: {e}")

def test_message_formatting():
    """Test message formatting"""
    print("\n📝 Testing Message Formatting...")
    
    try:
        analyzer = TradingAnalyzer()
        
        # Create a mock analysis
        mock_analysis = {
            'symbol': 'SOL',
            'current_price': 150.25,
            'trend': 'Bullish',
            'rsi': 65.5,
            'ema50': 145.30,
            'ema200': 140.80,
            'macd': 2.5,
            'macd_signal': 2.1,
            'support': 145.00,
            'resistance': 155.00,
            'volume': 1000000,
            'volatility': 3.2,
            'technical_analysis': '🔍 Análise Técnica - SOL\n\n🟢 Recomendação: COMPRA\n🔥 Confiança: Alta',
            'news_summary': '📰 Notícias sobre SOL (2 itens)\n\n📈 Sentimento Geral: Positive'
        }
        
        message = analyzer.format_analysis_message(mock_analysis)
        print("  ✅ Message formatting successful")
        print(f"    Message length: {len(message)} characters")
        
    except Exception as e:
        print(f"  ❌ Message formatting error: {e}")

def main():
    """Run all tests"""
    print("🚀 CriptoTipic4 Bot Test Suite")
    print("=" * 50)
    
    test_environment()
    test_news_service()
    test_analysis_service()
    test_trading_analyzer()
    test_message_formatting()
    
    print("\n" + "=" * 50)
    print("✅ Test suite completed!")
    print("\n💡 Next steps:")
    print("1. (Opcional) Configure Twitter API key in .env file para mais notícias")
    print("2. Run the bot with: python app.py")
    print("3. Test with Telegram by sending 'SOL' to your bot")
    print("4. O bot agora usa apenas APIs gratuitas! 🎉")

if __name__ == "__main__":
    main()
