#!/usr/bin/env python3
"""
Test script for CriptoTipic4 bot functionality
"""

import os
import sys
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.trading import TradingAnalyzer
from services.ai_service import AIService

def test_environment():
    """Test if environment variables are properly loaded"""
    load_dotenv()
    
    print("🔍 Testing Environment Configuration...")
    
    # Check required environment variables
    required_vars = [
        'TELEGRAM_BOT_TOKEN',
        'TELEGRAM_CHAT_ID',
        'OPENROUTER_API_KEY'
    ]
    
    optional_vars = [
        'NEWS_API_KEY',
        'TWITTER_BEARER_TOKEN'
    ]
    
    print("\n✅ Required Variables:")
    for var in required_vars:
        value = os.getenv(var)
        if value and value != f'your_{var.lower()}_here':
            print(f"  {var}: ✓ Configured")
        else:
            print(f"  {var}: ❌ Missing or not configured")
    
    print("\n⚠️  Optional Variables:")
    for var in optional_vars:
        value = os.getenv(var)
        if value and value != f'your_{var.lower()}_here':
            print(f"  {var}: ✓ Configured")
        else:
            print(f"  {var}: ❌ Missing (some features will be limited)")

def test_ai_service():
    """Test AI service functionality"""
    print("\n🤖 Testing AI Service...")
    
    ai_service = AIService()
    
    # Test with a simple prompt
    test_prompt = "Provide a brief analysis of Bitcoin's current market position."
    
    try:
        # Test news analysis with empty data
        news_result = ai_service.analyze_news([])
        print(f"  News Analysis (empty): {news_result[:50]}...")
        
        # Test market sentiment analysis
        technical_data = "Current Price: $45000, RSI: 55, Trend: Bullish"
        sentiment_result = ai_service.analyze_market_sentiment(technical_data)
        print(f"  Market Sentiment: {sentiment_result[:50]}...")
        
        # Test Twitter sentiment analysis
        twitter_result = ai_service.analyze_twitter_sentiment([])
        print(f"  Twitter Sentiment (empty): {twitter_result[:50]}...")
        
        print("  ✅ AI Service tests completed")
        
    except Exception as e:
        print(f"  ❌ AI Service error: {e}")

def test_trading_analyzer():
    """Test trading analyzer functionality"""
    print("\n📊 Testing Trading Analyzer...")
    
    try:
        analyzer = TradingAnalyzer()
        
        # Test with SOL (Solana)
        print("  Testing SOL analysis...")
        analysis = analyzer.analyze_market('SOL')
        
        if analysis:
            print("  ✅ Market analysis successful")
            print(f"    Symbol: {analysis['symbol']}")
            print(f"    Current Price: ${analysis['current_price']:.2f}")
            print(f"    RSI: {analysis['rsi']:.1f}")
            print(f"    Trend: {analysis['trend']}")
        else:
            print("  ❌ Market analysis failed")
            
    except Exception as e:
        print(f"  ❌ Trading Analyzer error: {e}")

def test_message_formatting():
    """Test message formatting"""
    print("\n📝 Testing Message Formatting...")
    
    try:
        analyzer = TradingAnalyzer()
        
        # Create a mock analysis
        mock_analysis = {
            'symbol': 'SOL',
            'current_price': 150.25,
            'trend': 'Bullish',
            'rsi': 65.5,
            'ema50': 145.30,
            'ema200': 140.80,
            'macd': 2.5,
            'macd_signal': 2.1,
            'support': 145.00,
            'resistance': 155.00,
            'volume': 1000000,
            'volatility': 3.2,
            'trading_insights': 'Test trading insights',
            'news_analysis': 'Test news analysis',
            'sentiment_analysis': 'Test sentiment analysis',
            'twitter_sentiment': 'Test twitter sentiment'
        }
        
        message = analyzer.format_analysis_message(mock_analysis)
        print("  ✅ Message formatting successful")
        print(f"    Message length: {len(message)} characters")
        
    except Exception as e:
        print(f"  ❌ Message formatting error: {e}")

def main():
    """Run all tests"""
    print("🚀 CriptoTipic4 Bot Test Suite")
    print("=" * 50)
    
    test_environment()
    test_ai_service()
    test_trading_analyzer()
    test_message_formatting()
    
    print("\n" + "=" * 50)
    print("✅ Test suite completed!")
    print("\n💡 Next steps:")
    print("1. Configure missing API keys in .env file")
    print("2. Run the bot with: python app.py")
    print("3. Test with Telegram by sending 'SOL' to your bot")

if __name__ == "__main__":
    main()
